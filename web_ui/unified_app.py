#!/usr/bin/env python3
"""
DyFlow 统一Web UI应用
整合所有Web UI功能到一个统一的应用中
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

# 添加src到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / 'src'))
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import structlog

# DyFlow imports
from src.supervisor import DyFlowSupervisor, SupervisorConfig
from src.tools.system_health_tool import SystemHealthTool
from src.tools.real_data_provider_tool import RealDataProviderTool
from src.utils.config import Config
from src.utils.helpers import setup_logging

# 设置日志
setup_logging()
logger = structlog.get_logger(__name__)

class UnifiedWebApp:
    """统一Web应用"""
    
    def __init__(self):
        self.app = FastAPI(
            title="DyFlow Dashboard",
            description="24/7自动化单边LP策略系统",
            version="3.1"
        )
        
        self.project_root = Path(__file__).parent.parent
        self.setup_middleware()
        self.setup_routes()
        self.setup_websocket()
        
        # 组件初始化
        self.supervisor: Optional[DyFlowSupervisor] = None
        self.health_tool: Optional[SystemHealthTool] = None
        self.data_provider: Optional[RealDataProviderTool] = None
        self.websocket_connections: List[WebSocket] = []
        
        # 数据缓存
        self.cached_data = {
            "pools": [],
            "positions": [],
            "system_health": {},
            "last_update": None
        }
    
    def setup_middleware(self):
        """设置中间件"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/")
        async def root():
            """根路径 - 自动选择UI"""
            # 检查React UI是否可用
            react_dist = self.project_root / "react-ui" / "dist" / "index.html"
            if react_dist.exists():
                return FileResponse(str(react_dist))
            
            # 使用简单HTML UI
            html_file = self.project_root / "dyflow_enhanced_dashboard.html"
            if html_file.exists():
                return FileResponse(str(html_file))
            
            # 返回基本信息
            return {
                "message": "DyFlow Dashboard",
                "version": "3.1",
                "status": "running",
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.get("/api/health")
        async def health_check():
            """健康检查"""
            try:
                if not self.health_tool:
                    self.health_tool = SystemHealthTool()
                
                health_data = await self.health_tool.get_system_health()
                return {
                    "status": "healthy",
                    "timestamp": datetime.now().isoformat(),
                    "data": health_data
                }
            except Exception as e:
                logger.error("health_check_failed", error=str(e))
                return {
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
        
        @self.app.get("/api/pools")
        async def get_pools():
            """获取池子数据"""
            try:
                if not self.data_provider:
                    self.data_provider = RealDataProviderTool()
                
                # 获取BSC池子
                bsc_pools = await self.data_provider.get_bsc_pools()
                
                # 获取Solana池子
                solana_pools = await self.data_provider.get_solana_pools()
                
                pools_data = {
                    "bsc_pools": bsc_pools,
                    "solana_pools": solana_pools,
                    "total_count": len(bsc_pools) + len(solana_pools),
                    "timestamp": datetime.now().isoformat()
                }
                
                # 更新缓存
                self.cached_data["pools"] = pools_data
                self.cached_data["last_update"] = datetime.now()
                
                return pools_data
                
            except Exception as e:
                logger.error("get_pools_failed", error=str(e))
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/positions")
        async def get_positions():
            """获取持仓数据"""
            try:
                # 这里应该从数据库或supervisor获取真实持仓数据
                # 暂时返回模拟数据
                positions_data = {
                    "active_positions": [],
                    "total_value": 0.0,
                    "pnl": 0.0,
                    "timestamp": datetime.now().isoformat()
                }
                
                self.cached_data["positions"] = positions_data
                return positions_data
                
            except Exception as e:
                logger.error("get_positions_failed", error=str(e))
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/system/status")
        async def get_system_status():
            """获取系统状态"""
            try:
                status_data = {
                    "supervisor_running": self.supervisor is not None,
                    "websocket_connections": len(self.websocket_connections),
                    "last_data_update": self.cached_data.get("last_update"),
                    "uptime": datetime.now().isoformat(),
                    "components": {
                        "data_provider": self.data_provider is not None,
                        "health_tool": self.health_tool is not None,
                        "supervisor": self.supervisor is not None
                    }
                }
                
                return status_data
                
            except Exception as e:
                logger.error("get_system_status_failed", error=str(e))
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/system/start")
        async def start_system():
            """启动系统"""
            try:
                if not self.supervisor:
                    config = Config.load_from_file("config/config.yaml")
                    self.supervisor = DyFlowSupervisor(config)
                    await self.supervisor.initialize()
                
                if not self.supervisor.is_running:
                    await self.supervisor.start()
                
                return {
                    "status": "started",
                    "timestamp": datetime.now().isoformat()
                }
                
            except Exception as e:
                logger.error("start_system_failed", error=str(e))
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/system/stop")
        async def stop_system():
            """停止系统"""
            try:
                if self.supervisor and self.supervisor.is_running:
                    await self.supervisor.stop()
                
                return {
                    "status": "stopped",
                    "timestamp": datetime.now().isoformat()
                }
                
            except Exception as e:
                logger.error("stop_system_failed", error=str(e))
                raise HTTPException(status_code=500, detail=str(e))
        
        # 静态文件服务
        react_dist = self.project_root / "react-ui" / "dist"
        if react_dist.exists():
            self.app.mount("/static", StaticFiles(directory=str(react_dist)), name="static")
    
    def setup_websocket(self):
        """设置WebSocket"""
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            await websocket.accept()
            self.websocket_connections.append(websocket)
            
            try:
                while True:
                    # 发送实时数据
                    data = {
                        "type": "update",
                        "pools": self.cached_data.get("pools", []),
                        "positions": self.cached_data.get("positions", []),
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    await websocket.send_json(data)
                    await asyncio.sleep(5)  # 每5秒发送一次更新
                    
            except WebSocketDisconnect:
                self.websocket_connections.remove(websocket)
            except Exception as e:
                logger.error("websocket_error", error=str(e))
                if websocket in self.websocket_connections:
                    self.websocket_connections.remove(websocket)
    
    async def broadcast_update(self, data: Dict[str, Any]):
        """广播更新到所有WebSocket连接"""
        if not self.websocket_connections:
            return
        
        disconnected = []
        for websocket in self.websocket_connections:
            try:
                await websocket.send_json(data)
            except Exception:
                disconnected.append(websocket)
        
        # 清理断开的连接
        for websocket in disconnected:
            self.websocket_connections.remove(websocket)
    
    async def start_background_tasks(self):
        """启动后台任务"""
        async def data_update_task():
            """数据更新任务"""
            while True:
                try:
                    # 更新池子数据
                    if self.data_provider:
                        bsc_pools = await self.data_provider.get_bsc_pools()
                        solana_pools = await self.data_provider.get_solana_pools()
                        
                        pools_data = {
                            "bsc_pools": bsc_pools,
                            "solana_pools": solana_pools,
                            "total_count": len(bsc_pools) + len(solana_pools),
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        self.cached_data["pools"] = pools_data
                        self.cached_data["last_update"] = datetime.now()
                        
                        # 广播更新
                        await self.broadcast_update({
                            "type": "pools_update",
                            "data": pools_data
                        })
                    
                    await asyncio.sleep(30)  # 每30秒更新一次
                    
                except Exception as e:
                    logger.error("data_update_task_error", error=str(e))
                    await asyncio.sleep(60)  # 错误时等待更长时间
        
        # 启动后台任务
        asyncio.create_task(data_update_task())
    
    def run(self, host: str = "0.0.0.0", port: int = 8000):
        """运行应用"""
        logger.info("unified_web_app_starting", host=host, port=port)
        
        # 启动后台任务
        asyncio.create_task(self.start_background_tasks())
        
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info",
            access_log=True
        )

def main():
    """主函数"""
    print("🌐 启动DyFlow统一Web UI")
    print("=" * 50)
    
    app = UnifiedWebApp()
    
    try:
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())

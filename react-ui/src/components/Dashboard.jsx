import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from './ui/tabs'
import { Activity, TrendingUp, DollarSign, Zap, ExternalLink, RefreshCw, Wifi, WifiOff, Bot, AlertTriangle, Eye, Target, Shield, Cpu, Database, Globe, PieChart, BarChart3, TrendingDown, Clock, CheckCircle, XCircle, Loader } from 'lucide-react'
import { formatCurrency, formatPercentage } from '../lib/utils'
import { useWebSocket } from '../contexts/WebSocketContext'
import PoolMonitor from './PoolMonitor'
import AgentMonitor from './AgentMonitor'
import SystemOverview from './SystemOverview'
import PortfolioOverview from './PortfolioOverview'
import PhaseMonitor from './PhaseMonitor'
import WorkflowControl from './WorkflowControl_new'

const Dashboard = () => {
  const {
    isConnected,
    connectionStatus,
    dashboardData,
    forceUpdate,
    evaluatePool,
    emergencyExit
  } = useWebSocket()

  const [activeTab, setActiveTab] = useState('overview')

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  }

  const handlePoolEvaluate = (pool) => {
    evaluatePool(pool)
  }

  const handleEmergencyExit = () => {
    if (window.confirm('確定要執行緊急退出嗎？這將關閉所有LP持倉。')) {
      emergencyExit()
    }
  }

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-500'
      case 'connecting': return 'text-yellow-500'
      case 'disconnected': return 'text-gray-500'
      case 'error': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return '已連接'
      case 'connecting': return '連接中...'
      case 'disconnected': return '未連接'
      case 'error': return '連接錯誤'
      default: return '未知狀態'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-7xl mx-auto space-y-6"
      >
        {/* Header with Connection Status */}
        <motion.div variants={itemVariants} className="flex justify-between items-center mb-8">
          <div className="text-center flex-1">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              DyFlow Dashboard
            </h1>
            <p className="text-gray-600">
              24/7 自動化流動性挖礦策略系統
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              {isConnected ? (
                <Wifi className={`h-5 w-5 ${getConnectionStatusColor()}`} />
              ) : (
                <WifiOff className={`h-5 w-5 ${getConnectionStatusColor()}`} />
              )}
              <span className={`text-sm ${getConnectionStatusColor()}`}>
                {getConnectionStatusText()}
              </span>
            </div>

            <Button
              size="sm"
              variant="destructive"
              onClick={handleEmergencyExit}
              disabled={!isConnected}
            >
              <AlertTriangle className="h-4 w-4 mr-1" />
              緊急退出
            </Button>
          </div>
        </motion.div>

        {/* Enhanced System Overview */}
        <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* Pool Monitoring Panel */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5 text-blue-500" />
                <span>池子監控中心</span>
              </CardTitle>
              <CardDescription>實時監控高收益LP池子</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{dashboardData.pool_data?.length || 0}</div>
                  <div className="text-sm text-gray-500">總池子數</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {dashboardData.pool_data?.filter(p => p.chain === 'bsc').length || 0}
                  </div>
                  <div className="text-sm text-gray-500">BSC池子</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {dashboardData.pool_data?.filter(p => p.chain === 'solana').length || 0}
                  </div>
                  <div className="text-sm text-gray-500">Solana池子</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {dashboardData.pool_data?.filter(p => p.apr > 100).length || 0}
                  </div>
                  <div className="text-sm text-gray-500">高收益池</div>
                </div>
              </div>

              {/* Top Pools Preview */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm text-gray-700">🔥 熱門池子</h4>
                {(dashboardData.pool_data || []).slice(0, 3).map((pool, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Badge variant={pool.chain === 'bsc' ? 'secondary' : 'default'} className="text-xs">
                        {pool.chain === 'bsc' ? 'BSC' : 'SOL'}
                      </Badge>
                      <span className="font-medium text-sm">{pool.pair}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-green-600 font-semibold text-sm">{formatPercentage(pool.apr)}</div>
                      <div className="text-xs text-gray-500">{formatCurrency(pool.tvl_usd)}</div>
                    </div>
                  </div>
                ))}
                {(!dashboardData.pool_data || dashboardData.pool_data.length === 0) && (
                  <div className="text-center py-4 text-gray-500 text-sm">
                    正在載入池子數據...
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Agents Status Panel */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bot className="h-5 w-5 text-green-500" />
                <span>AI Agents 狀態</span>
              </CardTitle>
              <CardDescription>智能代理運行狀態</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Supervisor Status */}
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Cpu className="h-4 w-4 text-blue-500" />
                  <span className="font-medium text-sm">Supervisor</span>
                </div>
                <Badge variant={dashboardData.system_status?.supervisor?.is_running ? "default" : "destructive"}>
                  {dashboardData.system_status?.supervisor?.is_running ? "運行中" : "已停止"}
                </Badge>
              </div>

              {/* Risk Sentinel */}
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-orange-500" />
                  <span className="font-medium text-sm">Risk Sentinel</span>
                </div>
                <Badge variant="default">監控中</Badge>
              </div>

              {/* Planner Agent */}
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-green-500" />
                  <span className="font-medium text-sm">Planner</span>
                </div>
                <Badge variant="default">策略中</Badge>
              </div>

              {/* Data Provider */}
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-violet-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4 text-purple-500" />
                  <span className="font-medium text-sm">Data Provider</span>
                </div>
                <Badge variant="default">同步中</Badge>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* API Connections Status */}
        <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Meteora API</CardTitle>
              <Globe className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <Badge variant={dashboardData.system_status?.connections?.meteora_api === "connected" ? "default" : "destructive"}>
                  {dashboardData.system_status?.connections?.meteora_api === "connected" ? "已連接" : "未連接"}
                </Badge>
                <div className="text-xs text-gray-500">Solana DLMM</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">PancakeSwap API</CardTitle>
              <Zap className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <Badge variant={dashboardData.system_status?.connections?.pancakeswap_api === "connected" ? "default" : "destructive"}>
                  {dashboardData.system_status?.connections?.pancakeswap_api === "connected" ? "已連接" : "未連接"}
                </Badge>
                <div className="text-xs text-gray-500">BSC V3</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">CoinGecko API</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <Badge variant={dashboardData.system_status?.connections?.coingecko_api === "connected" ? "default" : "destructive"}>
                  {dashboardData.system_status?.connections?.coingecko_api === "connected" ? "已連接" : "未連接"}
                </Badge>
                <div className="text-xs text-gray-500">價格數據</div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Main Content Tabs */}
        <motion.div variants={itemVariants}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="overview">系統概覽</TabsTrigger>
              <TabsTrigger value="portfolio">投資組合</TabsTrigger>
              <TabsTrigger value="phases">系統狀態</TabsTrigger>
              <TabsTrigger value="pools">池子監控</TabsTrigger>
              <TabsTrigger value="agents">Agent狀態</TabsTrigger>
              <TabsTrigger value="positions">LP持倉</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <SystemOverview dashboardData={dashboardData} />
            </TabsContent>

            <TabsContent value="portfolio" className="space-y-4">
              <PortfolioOverview
                portfolioData={dashboardData.portfolio_data}
                positions={dashboardData.positions}
              />
            </TabsContent>

            <TabsContent value="phases" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-1">
                  <WorkflowControl
                    workflowStatus={{
                      current_phase: dashboardData.current_phase,
                      phase_status: dashboardData.phase_status,
                      completed_phases: Object.values(dashboardData.phase_status || {}).filter(p => p.status === 'completed').length,
                      total_phases: 9,
                      progress_percentage: (Object.values(dashboardData.phase_status || {}).filter(p => p.status === 'completed').length / 9) * 100,
                      is_running: Object.values(dashboardData.phase_status || {}).some(p => p.status === 'running'),
                      agents: dashboardData.agents_status?.agents || [],
                      last_updated: dashboardData.last_update
                    }}
                    onWorkflowStart={() => forceUpdate()}
                    onWorkflowReset={() => forceUpdate()}
                  />
                </div>
                <div className="lg:col-span-2">
                  <PhaseMonitor
                    phaseStatus={dashboardData.phase_status}
                    currentPhase={dashboardData.current_phase}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="pools" className="space-y-4">
              <PoolMonitor
                pools={dashboardData.pool_data || []}
                onEvaluatePool={handlePoolEvaluate}
              />
            </TabsContent>

            <TabsContent value="agents" className="space-y-4">
              <AgentMonitor
                systemStatus={dashboardData.system_status}
                agentLogs={dashboardData.agent_logs}
                agentsStatus={dashboardData.agents_status}
              />
            </TabsContent>

            <TabsContent value="positions" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>LP持倉</CardTitle>
                  <CardDescription>
                    當前活躍的流動性挖礦持倉 ({dashboardData.positions?.length || 0} 個持倉)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">池子</th>
                          <th className="text-left p-2">網絡</th>
                          <th className="text-left p-2">流動性</th>
                          <th className="text-left p-2">PnL</th>
                          <th className="text-left p-2">IL</th>
                          <th className="text-left p-2">APR</th>
                          <th className="text-left p-2">狀態</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(dashboardData.positions || []).map((position, index) => (
                          <motion.tr
                            key={position.id || index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="border-b hover:bg-gray-50"
                          >
                            <td className="p-2 font-medium">{position.pool}</td>
                            <td className="p-2">
                              <Badge variant={position.chain === 'bsc' ? 'secondary' : 'default'}>
                                {position.chain === 'bsc' ? 'BSC' : 'SOL'}
                              </Badge>
                            </td>
                            <td className="p-2">{formatCurrency(position.liquidity_usd)}</td>
                            <td className={`p-2 font-semibold ${position.pnl_pct >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {position.pnl_pct >= 0 ? '+' : ''}{formatPercentage(position.pnl_pct)}
                            </td>
                            <td className={`p-2 font-semibold ${position.il_pct >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {formatPercentage(position.il_pct)}
                            </td>
                            <td className="p-2 text-green-600 font-semibold">
                              {formatPercentage(position.apr)}
                            </td>
                            <td className="p-2">
                              <Badge variant={position.status === 'active' ? 'default' : 'secondary'}>
                                {position.status === 'active' ? '活躍' : '監控中'}
                              </Badge>
                            </td>
                          </motion.tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>
      </motion.div>
    </div>
  )
}

export default Dashboard

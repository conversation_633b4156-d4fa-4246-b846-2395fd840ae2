import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import {
  Play,
  Square,
  RotateCcw,
  Zap,
  CheckCircle,
  AlertTriangle,
  Clock,
  Loader,
  Bot,
  Brain,
  Pause
} from 'lucide-react'

const WorkflowControl = ({ onWorkflowStart, onWorkflowReset, workflowStatus }) => {
  const [isStarting, setIsStarting] = useState(false)
  const [isResetting, setIsResetting] = useState(false)
  const [agentTakeover, setAgentTakeover] = useState(false)

  const handleStartAgent = async () => {
    setIsStarting(true)
    try {
      const response = await fetch('http://localhost:8001/api/agent/takeover', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json()
        console.log('✅ Agent 接管啟動成功:', result)
        setAgentTakeover(true)

        // 顯示成功提示
        if (window.showToast) {
          window.showToast('🤖 AI Agent 接管已啟動！系統進入自主管理模式', 'success')
        }

        if (onWorkflowStart) onWorkflowStart()
      } else {
        console.error('❌ Agent 接管啟動失敗:', response.statusText)
        if (window.showToast) {
          window.showToast('❌ Agent 接管啟動失敗', 'error')
        }
      }
    } catch (error) {
      console.error('❌ Agent 接管啟動異常:', error)
      if (window.showToast) {
        window.showToast('❌ 網絡連接失敗，請檢查 API 服務', 'error')
      }
    } finally {
      setIsStarting(false)
    }
  }

  const handleStopAgent = async () => {
    setIsResetting(true)
    try {
      const response = await fetch('http://localhost:8001/api/agent/stop', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json()
        console.log('✅ Agent 停止成功:', result)
        setAgentTakeover(false)

        // 顯示成功提示
        if (window.showToast) {
          window.showToast('⏸️ AI Agent 接管已停止，系統回到手動模式', 'info')
        }

        if (onWorkflowReset) onWorkflowReset()
      } else {
        console.error('❌ Agent 停止失敗:', response.statusText)
        if (window.showToast) {
          window.showToast('❌ Agent 停止失敗', 'error')
        }
      }
    } catch (error) {
      console.error('❌ Agent 停止異常:', error)
      if (window.showToast) {
        window.showToast('❌ 網絡連接失敗，請檢查 API 服務', 'error')
      }
    } finally {
      setIsResetting(false)
    }
  }

  const getAgentStatusBadge = () => {
    if (!workflowStatus) {
      return <Badge variant="outline">系統未就緒</Badge>
    }

    const completedPhases = workflowStatus.completed_phases || 0
    const totalPhases = workflowStatus.total_phases || 9

    if (agentTakeover) {
      return (
        <Badge className="bg-green-100 text-green-800">
          <Bot className="h-3 w-3 mr-1" />
          Agent 已接管
        </Badge>
      )
    }

    if (completedPhases === totalPhases) {
      return (
        <Badge className="bg-blue-100 text-blue-800">
          <CheckCircle className="h-3 w-3 mr-1" />
          系統就緒
        </Badge>
      )
    }

    if (workflowStatus.is_running) {
      return (
        <Badge className="bg-yellow-100 text-yellow-800">
          <Loader className="h-3 w-3 mr-1 animate-spin" />
          系統初始化中
        </Badge>
      )
    }

    return (
      <Badge variant="outline">
        <Square className="h-3 w-3 mr-1" />
        等待初始化
      </Badge>
    )
  }

  const getAgentSummary = () => {
    if (!workflowStatus || !workflowStatus.agents) {
      return { total: 0, active: 0 }
    }

    // 處理新的 Agno API 數據結構 (agents 是數組)
    const agents = Array.isArray(workflowStatus.agents)
      ? workflowStatus.agents
      : Object.values(workflowStatus.agents)

    return {
      total: agents.length,
      active: agents.filter(agent =>
        agent.status === 'active' || agent.status === 'initialized'
      ).length
    }
  }

  const agentSummary = getAgentSummary()

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Brain className="h-5 w-5 text-purple-500" />
          <span>AI Agent 控制中心</span>
        </CardTitle>
        <CardDescription>
          系統初始化完成後，啟動 AI Agent 自主管理和決策
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 狀態概覽 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {workflowStatus?.completed_phases || 0}/{workflowStatus?.total_phases || 9}
            </div>
            <div className="text-sm text-gray-500">系統初始化</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {agentSummary.active}/{agentSummary.total}
            </div>
            <div className="text-sm text-gray-500">活躍 Agent</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {agentTakeover ? '1' : '0'}
            </div>
            <div className="text-sm text-gray-500">Agent 接管</div>
          </div>
        </div>

        {/* 狀態標籤 */}
        <div className="flex justify-center">
          {getAgentStatusBadge()}
        </div>

        {/* 進度條 */}
        {workflowStatus && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>整體進度</span>
              <span>{(workflowStatus.progress_percentage || 0).toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${workflowStatus.progress_percentage || 0}%` }}
              />
            </div>
          </div>
        )}

        {/* 控制按鈕 */}
        <div className="flex space-x-3">
          {!agentTakeover ? (
            <Button
              onClick={handleStartAgent}
              disabled={isStarting || (workflowStatus && workflowStatus.completed_phases < workflowStatus.total_phases)}
              className="flex-1 bg-purple-600 hover:bg-purple-700"
            >
              {isStarting ? (
                <Loader className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Bot className="h-4 w-4 mr-2" />
              )}
              {isStarting ? '啟動中...' : '啟動 Agent 接管'}
            </Button>
          ) : (
            <Button
              variant="outline"
              onClick={handleStopAgent}
              disabled={isResetting}
              className="flex-1 border-red-300 text-red-600 hover:bg-red-50"
            >
              {isResetting ? (
                <Loader className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Pause className="h-4 w-4 mr-2" />
              )}
              {isResetting ? '停止中...' : '停止 Agent'}
            </Button>
          )}
        </div>

        {/* Agent 狀態摘要 */}
        {workflowStatus && workflowStatus.agents && workflowStatus.agents.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm">活躍 Agent ({workflowStatus.agents.length} 個)</h4>
            <div className="grid grid-cols-2 gap-2">
              {workflowStatus.agents.map((agent, index) => (
                <div key={agent.name || index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                  <div className={`w-2 h-2 rounded-full ${
                    agent.status === 'active' || agent.status === 'initialized' ? 'bg-green-500' : 'bg-gray-400'
                  }`} />
                  <span className="text-xs font-medium">{agent.name}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 最後更新時間 */}
        {workflowStatus && workflowStatus.last_updated && (
          <div className="text-xs text-gray-500 text-center">
            最後更新: {new Date(workflowStatus.last_updated).toLocaleTimeString()}
          </div>
        )}

        {/* 狀態信息 */}
        {workflowStatus && workflowStatus.is_running && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-lg"
          >
            <AlertTriangle className="h-4 w-4 text-blue-600" />
            <span className="text-sm text-blue-800">
              系統正在初始化中，請等待完成後啟動 Agent
            </span>
          </motion.div>
        )}

        {agentTakeover && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg"
          >
            <Bot className="h-4 w-4 text-green-600" />
            <span className="text-sm text-green-800">
              AI Agent 已接管系統，正在自主管理和決策中
            </span>
          </motion.div>
        )}
      </CardContent>
    </Card>
  )
}

export default WorkflowControl

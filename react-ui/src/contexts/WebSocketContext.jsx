import React, { createContext, useContext, useEffect, useState, useRef } from 'react'

const WebSocketContext = createContext()

export const useWebSocket = () => {
  const context = useContext(WebSocketContext)
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider')
  }
  return context
}

export const WebSocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null)
  const [isConnected, setIsConnected] = useState(false)
  const [dashboardData, setDashboardData] = useState({
    system_status: {
      supervisor: { is_running: false, agno_enabled: false, uptime_seconds: 0 },
      connections: {
        meteora_api: "disconnected",
        pancakeswap_api: "disconnected",
        coingecko_api: "disconnected"
      }
    },
    pool_data: [],
    positions: [],
    portfolio_data: {
      total_value_usd: 0,
      total_pnl_usd: 0,
      total_il_usd: 0,
      avg_apr: 0,
      fees_24h_usd: 0,
      risk_score: 0
    },
    phase_status: {},
    current_phase: 0,
    risk_alerts: [],
    agent_logs: [],
    agents_status: {},
    token_prices: {},
    last_update: new Date().toISOString()
  })
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const reconnectTimeoutRef = useRef(null)
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5

  // 加載初始數據的函數
  const loadInitialData = async () => {
    try {
      console.log('🔄 加載初始數據...')

      // 並發請求所有必要數據
      const [systemData, agentData, poolData] = await Promise.all([
        fetch('http://localhost:8001/api/system/status').then(res => res.json()),
        fetch('http://localhost:8001/api/agents').then(res => res.json()),
        fetch('http://localhost:8001/api/pools').then(res => res.json())
      ])

      console.log('📊 收到 Agno API 數據:', {
        overall_status: systemData.overall_status,
        current_phase: systemData.current_phase,
        agents: agentData.agents?.length || 0,
        phases: systemData.phases?.length || 0,
        pools: poolData.pools?.length || 0,
        progress: systemData.progress_percentage
      })

      // 使用真實的池子數據
      const realPoolData = poolData.pools || []

      // 生成Agent日志
      const agentLogs = []
      if (agentData.agents) {
        agentData.agents.forEach(agent => {
          agentLogs.push({
            timestamp: agent.last_activity || new Date().toISOString(),
            agent: agent.name,
            level: agent.status === 'initialized' ? 'info' : 'warning',
            message: `${agent.name} - Phase ${agent.phase} - 狀態: ${agent.status}`
          })
        })
      }

      // 生成模擬投資組合數據
      const mockPositions = [
        {
          id: 'pos_1',
          pool: 'SOL/USDC',
          chain: 'solana',
          strategy: 'SPOT_IMBALANCED_DAMM',
          liquidity_usd: 5000,
          pnl_usd: 125,
          pnl_pct: 2.5,
          il_usd: -45,
          il_pct: -0.9,
          apr: 185.5,
          fees_24h_usd: 25.5,
          status: 'active'
        },
        {
          id: 'pos_2',
          pool: 'BNB/USDT',
          chain: 'bsc',
          strategy: 'CURVE_BALANCED',
          liquidity_usd: 3000,
          pnl_usd: 85,
          pnl_pct: 2.8,
          il_usd: -20,
          il_pct: -0.7,
          apr: 145.2,
          fees_24h_usd: 12.3,
          status: 'active'
        }
      ]

      // 設置數據
      const newData = {
        system_status: {
          supervisor: { is_running: true, agno_enabled: true, uptime_seconds: 0 },
          connections: {
            meteora_api: realPoolData.some(p => p.chain === 'solana') ? "connected" : "disconnected",
            pancakeswap_api: realPoolData.some(p => p.chain === 'bsc') ? "connected" : "disconnected",
            coingecko_api: "connected"
          }
        },
        pool_data: realPoolData,
        positions: mockPositions,
        portfolio_data: {
          total_value_usd: 8000,
          total_pnl_usd: 210,
          total_il_usd: -65,
          avg_apr: 165.35,
          fees_24h_usd: 37.8,
          risk_score: 75
        },
        // 使用 Agno API 的階段數據
        phase_status: systemData.phases ? systemData.phases.reduce((acc, phase) => {
          acc[phase.phase_id] = {
            status: phase.status,
            started_at: phase.started_at,
            completed_at: phase.completed_at,
            duration_seconds: phase.duration_seconds,
            agent_name: phase.agent_name
          }
          return acc
        }, {}) : {},
        current_phase: systemData.current_phase || 0,
        risk_alerts: [],
        agent_logs: agentLogs,
        agents_status: agentData,
        token_prices: {},
        last_update: new Date().toISOString()
      }

      setDashboardData(newData)
      console.log('✅ 初始數據加載完成')

    } catch (error) {
      console.error('❌ 加載初始數據失敗:', error)
    }
  }

  const connectWebSocket = () => {
    try {
      // 暫時禁用 WebSocket，改為純 API 模式
      console.log('🔌 使用純 API 模式，跳過 WebSocket 連接')
      setConnectionStatus('connected')
      setIsConnected(true)

      // 直接加載初始數據
      loadInitialData()
      return

      ws.onopen = () => {
        console.log('✅ WebSocket連接成功')
        setSocket(ws)
        setIsConnected(true)
        setConnectionStatus('connected')
        reconnectAttempts.current = 0

        // 连接后立即请求最新数据和Agent状态
        setTimeout(() => {
          console.log('🔄 请求最新数据和Agent状态...')

          // 并发请求所有必要数据 (使用新的 Agno API 端點)
          Promise.all([
            fetch('http://localhost:8001/api/system/status').then(res => res.json()),
            fetch('http://localhost:8001/api/agents').then(res => res.json()),
            fetch('http://localhost:8001/api/pools').then(res => res.json()),
            // 暫時使用模擬數據，因為這些端點還沒實現
            Promise.resolve({}), // portfolio
            Promise.resolve([])  // positions
          ])
            .then(([systemData, agentData, poolData, portfolioData, positionsData]) => {
              console.log('📊 收到 Agno API 数据:', {
                overall_status: systemData.overall_status,
                current_phase: systemData.current_phase,
                agents: agentData.agents?.length || 0,
                phases: systemData.phases?.length || 0,
                pools: poolData.pools?.length || 0,
                progress: systemData.progress_percentage
              })

              // 使用真實的池子數據
              const realPoolData = poolData.pools || []

              // 生成Agent日志 (使用新的 Agno API 數據結構)
              const agentLogs = []
              if (agentData.agents) {
                agentData.agents.forEach(agent => {
                  agentLogs.push({
                    timestamp: agent.last_activity || new Date().toISOString(),
                    agent: agent.name,
                    level: agent.status === 'initialized' ? 'info' : 'warning',
                    message: `${agent.name} - Phase ${agent.phase} - 状态: ${agent.status}`
                  })
                })
              }

              // 生成模擬投資組合數據
              const mockPositions = [
                {
                  id: 'pos_1',
                  pool: 'SOL/USDC',
                  chain: 'solana',
                  strategy: 'SPOT_IMBALANCED_DAMM',
                  liquidity_usd: 5000,
                  pnl_usd: 125,
                  pnl_pct: 2.5,
                  il_usd: -45,
                  il_pct: -0.9,
                  apr: 185.5,
                  fees_24h_usd: 25.5,
                  status: 'active'
                },
                {
                  id: 'pos_2',
                  pool: 'BNB/USDT',
                  chain: 'bsc',
                  strategy: 'CURVE_BALANCED',
                  liquidity_usd: 3000,
                  pnl_usd: 85,
                  pnl_pct: 2.8,
                  il_usd: -20,
                  il_pct: -0.7,
                  apr: 145.2,
                  fees_24h_usd: 12.3,
                  status: 'active'
                }
              ]

              // 使用新的 Agno API 數據結構
              const newData = {
                system_status: {
                  supervisor: { is_running: true, agno_enabled: true, uptime_seconds: 0 },
                  connections: {
                    meteora_api: realPoolData.some(p => p.chain === 'solana') ? "connected" : "disconnected",
                    pancakeswap_api: realPoolData.some(p => p.chain === 'bsc') ? "connected" : "disconnected",
                    coingecko_api: "connected"
                  }
                },
                pool_data: realPoolData,
                positions: positionsData.length > 0 ? positionsData : mockPositions,
                portfolio_data: portfolioData || {
                  total_value_usd: 8000,
                  total_pnl_usd: 210,
                  total_il_usd: -65,
                  avg_apr: 165.35,
                  fees_24h_usd: 37.8,
                  risk_score: 75
                },
                // 使用 Agno API 的階段數據
                phase_status: systemData.phases ? systemData.phases.reduce((acc, phase) => {
                  acc[phase.phase_id] = {
                    status: phase.status,
                    started_at: phase.started_at,
                    completed_at: phase.completed_at,
                    duration_seconds: phase.duration_seconds,
                    agent_name: phase.agent_name
                  }
                  return acc
                }, {}) : {},
                current_phase: systemData.current_phase || 0,
                risk_alerts: [],
                agent_logs: agentLogs,
                agents_status: agentData, // 添加Agent状态数据
                token_prices: {},
                last_update: new Date().toISOString()
              }
              console.log('🔍 設置階段數據:', {
                phase_status: newData.phase_status,
                current_phase: newData.current_phase
              })
              setDashboardData(newData)
            })
            .catch(err => console.error('❌ API请求失败:', err))
        }, 1000)
      }

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          console.log('📨 收到WebSocket消息:', message.type)

          switch (message.type) {
            case 'initial_data':
            case 'dashboard_update':
            case 'real_data_update':
              // 处理实时数据更新
              if (message.prices || message.bsc_pools || message.solana_pools) {
                // 调试：记录接收到的Solana池子数据
                if (message.solana_pools && message.solana_pools.length > 0) {
                  console.log('🔍 接收到Solana池子数据:', message.solana_pools.slice(0, 3).map(p => ({
                    pair: p.pair,
                    tvl_usd: p.tvl_usd,
                    formatted_tvl: typeof p.tvl_usd === 'number' ? (p.tvl_usd > 1e12 ? `${(p.tvl_usd/1e12).toFixed(1)}T` : p.tvl_usd > 1e9 ? `${(p.tvl_usd/1e9).toFixed(1)}B` : `${(p.tvl_usd/1e6).toFixed(1)}M`) : 'N/A'
                  })))

                  // 检查是否有异常的数万亿数值
                  const abnormalPools = message.solana_pools.filter(p => p.tvl_usd > 1e12)
                  if (abnormalPools.length > 0) {
                    console.error('🚨 发现异常的数万亿TVL值:', abnormalPools.slice(0, 3).map(p => ({
                      pair: p.pair,
                      tvl_usd: p.tvl_usd,
                      formatted: `${(p.tvl_usd/1e12).toFixed(1)}T`
                    })))
                  }
                }

                const newData = {
                  ...dashboardData, // 保留現有數據
                  system_status: {
                    supervisor: { is_running: true, agno_enabled: true, uptime_seconds: 0 },
                    connections: {
                      meteora_api: message.solana_pools && message.solana_pools.length > 0 ? "connected" : "disconnected",
                      pancakeswap_api: message.bsc_pools && message.bsc_pools.length > 0 ? "connected" : "disconnected",
                      coingecko_api: message.prices && Object.keys(message.prices).length > 0 ? "connected" : "disconnected"
                    }
                  },
                  pool_data: [...(message.bsc_pools || []), ...(message.solana_pools || [])],
                  token_prices: message.prices || {},
                  last_update: message.timestamp || new Date().toISOString()
                }
                setDashboardData(newData)
              } else if (message.data) {
                setDashboardData(message.data)
              }
              break
            case 'command_response':
              console.log('🤖 Agent命令響應:', message)
              // 可以在這裡處理命令響應
              break
            default:
              console.log('❓ 未知消息類型:', message.type)
          }
        } catch (error) {
          console.error('❌ 解析WebSocket消息失敗:', error)
        }
      }

      ws.onclose = (event) => {
        console.log('🔌 WebSocket連接關閉:', event.code, event.reason)
        setSocket(null)
        setIsConnected(false)
        setConnectionStatus('disconnected')

        // 自動重連邏輯
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000)
          console.log(`🔄 ${delay/1000}秒後嘗試重連 (${reconnectAttempts.current + 1}/${maxReconnectAttempts})`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++
            connectWebSocket()
          }, delay)
        } else {
          console.log('❌ 達到最大重連次數，停止重連')
          setConnectionStatus('failed')
        }
      }

      ws.onerror = (error) => {
        console.error('❌ WebSocket錯誤:', error)
        setConnectionStatus('error')
      }

    } catch (error) {
      console.error('❌ 創建WebSocket連接失敗:', error)
      setConnectionStatus('error')
    }
  }

  const sendMessage = (message) => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify(message))
      console.log('📤 發送WebSocket消息:', message.type)
    } else {
      console.warn('⚠️ WebSocket未連接，無法發送消息')
    }
  }

  const forceUpdate = () => {
    sendMessage({ type: 'force_update' })

    // 同時刷新階段數據
    refreshPhaseData()
  }

  const refreshPhaseData = async () => {
    try {
      const response = await fetch('http://localhost:8001/api/system/status')
      if (response.ok) {
        const systemData = await response.json()

        // 轉換 Agno API 階段數據格式
        const phaseStatus = systemData.phases ? systemData.phases.reduce((acc, phase) => {
          acc[phase.phase_id] = {
            status: phase.status,
            started_at: phase.started_at,
            completed_at: phase.completed_at,
            duration_seconds: phase.duration_seconds,
            agent_name: phase.agent_name
          }
          return acc
        }, {}) : {}

        setDashboardData(prevData => ({
          ...prevData,
          phase_status: phaseStatus,
          current_phase: systemData.current_phase || 0
        }))
        console.log('✅ Agno 階段數據已刷新:', systemData.current_phase)
      }
    } catch (error) {
      console.error('❌ 刷新 Agno 階段數據失敗:', error)
    }
  }

  const executeAgentCommand = (command, data = {}) => {
    sendMessage({
      type: 'agent_command',
      command,
      ...data
    })
  }

  const evaluatePool = (poolData) => {
    sendMessage({
      type: 'agent_command',
      command: 'evaluate_pool',
      pool_data: poolData
    })
  }

  const emergencyExit = () => {
    sendMessage({
      type: 'agent_command',
      command: 'emergency_exit'
    })
  }

  useEffect(() => {
    connectWebSocket()

    // 設置定期刷新階段數據 (減少頻率)
    const phaseRefreshInterval = setInterval(() => {
      refreshPhaseData()
    }, 30000) // 每30秒刷新一次，減少不必要的請求

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
      if (socket) {
        socket.close()
      }
      clearInterval(phaseRefreshInterval)
    }
  }, [])

  const value = {
    socket,
    isConnected,
    connectionStatus,
    dashboardData,
    sendMessage,
    forceUpdate,
    executeAgentCommand,
    evaluatePool,
    emergencyExit,
    reconnect: connectWebSocket
  }

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  )
}

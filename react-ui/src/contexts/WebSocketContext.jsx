import React, { createContext, useContext, useEffect, useState, useRef } from 'react'

const WebSocketContext = createContext()

export const useWebSocket = () => {
  const context = useContext(WebSocketContext)
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider')
  }
  return context
}

export const WebSocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null)
  const [isConnected, setIsConnected] = useState(false)
  const [dashboardData, setDashboardData] = useState({
    system_status: {
      supervisor: { is_running: false, agno_enabled: false, uptime_seconds: 0 },
      connections: {
        meteora_api: "disconnected",
        pancakeswap_api: "disconnected",
        coingecko_api: "disconnected"
      }
    },
    pool_data: [],
    positions: [],
    portfolio_data: {
      total_value_usd: 0,
      total_pnl_usd: 0,
      total_il_usd: 0,
      avg_apr: 0,
      fees_24h_usd: 0,
      risk_score: 0
    },
    phase_status: {},
    current_phase: 0,
    risk_alerts: [],
    agent_logs: [],
    agents_status: {},
    token_prices: {},
    last_update: new Date().toISOString()
  })
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const reconnectTimeoutRef = useRef(null)
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5

  const connectWebSocket = () => {
    try {
      // 根據當前環境確定WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const host = window.location.hostname
      const port = window.location.port || (window.location.protocol === 'https:' ? '443' : '80')
      
      // 如果是開發環境，連接到FastAPI後端
      const wsUrl = process.env.NODE_ENV === 'development'
        ? 'ws://localhost:8001/ws'
        : `${protocol}//${host}:${port}/ws`

      console.log('🔌 正在連接WebSocket:', wsUrl)
      setConnectionStatus('connecting')

      const ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        console.log('✅ WebSocket連接成功')
        setSocket(ws)
        setIsConnected(true)
        setConnectionStatus('connected')
        reconnectAttempts.current = 0

        // 连接后立即请求最新数据和Agent状态
        setTimeout(() => {
          console.log('🔄 请求最新数据和Agent状态...')

          // 并发请求所有必要数据
          Promise.all([
            fetch('http://localhost:8001/api/real-data').then(res => res.json()),
            fetch('http://localhost:8001/api/agents/status').then(res => res.json()),
            fetch('http://localhost:8001/api/portfolio/status').then(res => res.json()).catch(() => ({})),
            fetch('http://localhost:8001/api/phases/status').then(res => res.json()).catch(() => ({})),
            fetch('http://localhost:8001/api/positions').then(res => res.json()).catch(() => ([]))
          ])
            .then(([poolData, agentData, portfolioData, phaseData, positionsData]) => {
              console.log('📊 收到API数据:', {
                bsc_pools: poolData.bsc_pools?.length || 0,
                solana_pools: poolData.solana_pools?.length || 0,
                agents: Object.keys(agentData.agents || {}).length,
                positions: positionsData?.length || 0,
                current_phase: phaseData.current_phase || 0
              })

              // 检查Solana池子是否有异常数值
              if (poolData.solana_pools && poolData.solana_pools.length > 0) {
                const abnormalPools = poolData.solana_pools.filter(p => p.tvl_usd > 1e12)
                if (abnormalPools.length > 0) {
                  console.error('🚨 API数据中发现异常的数万亿TVL值:', abnormalPools.slice(0, 3).map(p => ({
                    pair: p.pair,
                    tvl_usd: p.tvl_usd,
                    formatted: `${(p.tvl_usd/1e12).toFixed(1)}T`
                  })))
                } else {
                  console.log('✅ API数据正常，没有异常TVL值')
                }
              }

              // 生成Agent日志
              const agentLogs = []
              if (agentData.agents) {
                Object.entries(agentData.agents).forEach(([agentName, agentInfo]) => {
                  agentLogs.push({
                    timestamp: agentInfo.last_run || new Date().toISOString(),
                    agent: agentName,
                    level: agentInfo.status === 'active' ? 'info' : 'warning',
                    message: `${agentInfo.name} - 状态: ${agentInfo.status}, 成功率: ${agentInfo.success_rate}%`
                  })
                })
              }

              // 生成模擬投資組合數據
              const mockPositions = [
                {
                  id: 'pos_1',
                  pool: 'SOL/USDC',
                  chain: 'solana',
                  strategy: 'SPOT_IMBALANCED_DAMM',
                  liquidity_usd: 5000,
                  pnl_usd: 125,
                  il_usd: -45,
                  apr: 185.5,
                  fees_24h_usd: 25.5,
                  status: 'active'
                },
                {
                  id: 'pos_2',
                  pool: 'BNB/USDT',
                  chain: 'bsc',
                  strategy: 'CURVE_BALANCED',
                  liquidity_usd: 3000,
                  pnl_usd: 85,
                  il_usd: -20,
                  apr: 145.2,
                  fees_24h_usd: 12.3,
                  status: 'active'
                }
              ]

              // 手动触发数据更新
              const newData = {
                system_status: {
                  supervisor: { is_running: true, agno_enabled: true, uptime_seconds: 0 },
                  connections: {
                    meteora_api: poolData.solana_pools && poolData.solana_pools.length > 0 ? "connected" : "disconnected",
                    pancakeswap_api: poolData.bsc_pools && poolData.bsc_pools.length > 0 ? "connected" : "disconnected",
                    coingecko_api: poolData.prices && Object.keys(poolData.prices).length > 0 ? "connected" : "disconnected"
                  }
                },
                pool_data: [...(poolData.bsc_pools || []), ...(poolData.solana_pools || [])],
                positions: positionsData.length > 0 ? positionsData : mockPositions,
                portfolio_data: portfolioData || {
                  total_value_usd: 8000,
                  total_pnl_usd: 210,
                  total_il_usd: -65,
                  avg_apr: 165.35,
                  fees_24h_usd: 37.8,
                  risk_score: 75
                },
                phase_status: phaseData.phase_status || {},
                current_phase: phaseData.current_phase || 0,
                risk_alerts: [],
                agent_logs: agentLogs,
                agents_status: agentData, // 添加Agent状态数据
                token_prices: poolData.prices || {},
                last_update: new Date().toISOString()
              }
              console.log('🔍 設置階段數據:', {
                phase_status: newData.phase_status,
                current_phase: newData.current_phase
              })
              setDashboardData(newData)
            })
            .catch(err => console.error('❌ API请求失败:', err))
        }, 1000)
      }

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          console.log('📨 收到WebSocket消息:', message.type)

          switch (message.type) {
            case 'initial_data':
            case 'dashboard_update':
            case 'real_data_update':
              // 处理实时数据更新
              if (message.prices || message.bsc_pools || message.solana_pools) {
                // 调试：记录接收到的Solana池子数据
                if (message.solana_pools && message.solana_pools.length > 0) {
                  console.log('🔍 接收到Solana池子数据:', message.solana_pools.slice(0, 3).map(p => ({
                    pair: p.pair,
                    tvl_usd: p.tvl_usd,
                    formatted_tvl: typeof p.tvl_usd === 'number' ? (p.tvl_usd > 1e12 ? `${(p.tvl_usd/1e12).toFixed(1)}T` : p.tvl_usd > 1e9 ? `${(p.tvl_usd/1e9).toFixed(1)}B` : `${(p.tvl_usd/1e6).toFixed(1)}M`) : 'N/A'
                  })))

                  // 检查是否有异常的数万亿数值
                  const abnormalPools = message.solana_pools.filter(p => p.tvl_usd > 1e12)
                  if (abnormalPools.length > 0) {
                    console.error('🚨 发现异常的数万亿TVL值:', abnormalPools.slice(0, 3).map(p => ({
                      pair: p.pair,
                      tvl_usd: p.tvl_usd,
                      formatted: `${(p.tvl_usd/1e12).toFixed(1)}T`
                    })))
                  }
                }

                const newData = {
                  ...dashboardData, // 保留現有數據
                  system_status: {
                    supervisor: { is_running: true, agno_enabled: true, uptime_seconds: 0 },
                    connections: {
                      meteora_api: message.solana_pools && message.solana_pools.length > 0 ? "connected" : "disconnected",
                      pancakeswap_api: message.bsc_pools && message.bsc_pools.length > 0 ? "connected" : "disconnected",
                      coingecko_api: message.prices && Object.keys(message.prices).length > 0 ? "connected" : "disconnected"
                    }
                  },
                  pool_data: [...(message.bsc_pools || []), ...(message.solana_pools || [])],
                  token_prices: message.prices || {},
                  last_update: message.timestamp || new Date().toISOString()
                }
                setDashboardData(newData)
              } else if (message.data) {
                setDashboardData(message.data)
              }
              break
            case 'command_response':
              console.log('🤖 Agent命令響應:', message)
              // 可以在這裡處理命令響應
              break
            default:
              console.log('❓ 未知消息類型:', message.type)
          }
        } catch (error) {
          console.error('❌ 解析WebSocket消息失敗:', error)
        }
      }

      ws.onclose = (event) => {
        console.log('🔌 WebSocket連接關閉:', event.code, event.reason)
        setSocket(null)
        setIsConnected(false)
        setConnectionStatus('disconnected')

        // 自動重連邏輯
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000)
          console.log(`🔄 ${delay/1000}秒後嘗試重連 (${reconnectAttempts.current + 1}/${maxReconnectAttempts})`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++
            connectWebSocket()
          }, delay)
        } else {
          console.log('❌ 達到最大重連次數，停止重連')
          setConnectionStatus('failed')
        }
      }

      ws.onerror = (error) => {
        console.error('❌ WebSocket錯誤:', error)
        setConnectionStatus('error')
      }

    } catch (error) {
      console.error('❌ 創建WebSocket連接失敗:', error)
      setConnectionStatus('error')
    }
  }

  const sendMessage = (message) => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify(message))
      console.log('📤 發送WebSocket消息:', message.type)
    } else {
      console.warn('⚠️ WebSocket未連接，無法發送消息')
    }
  }

  const forceUpdate = () => {
    sendMessage({ type: 'force_update' })

    // 同時刷新階段數據
    refreshPhaseData()
  }

  const refreshPhaseData = async () => {
    try {
      const response = await fetch('http://localhost:8001/api/phases/status')
      if (response.ok) {
        const phaseData = await response.json()
        setDashboardData(prevData => ({
          ...prevData,
          phase_status: phaseData.phase_status || {},
          current_phase: phaseData.current_phase || 0
        }))
        console.log('✅ 階段數據已刷新:', phaseData.current_phase)
      }
    } catch (error) {
      console.error('❌ 刷新階段數據失敗:', error)
    }
  }

  const executeAgentCommand = (command, data = {}) => {
    sendMessage({
      type: 'agent_command',
      command,
      ...data
    })
  }

  const evaluatePool = (poolData) => {
    sendMessage({
      type: 'agent_command',
      command: 'evaluate_pool',
      pool_data: poolData
    })
  }

  const emergencyExit = () => {
    sendMessage({
      type: 'agent_command',
      command: 'emergency_exit'
    })
  }

  useEffect(() => {
    connectWebSocket()

    // 設置定期刷新階段數據
    const phaseRefreshInterval = setInterval(() => {
      refreshPhaseData()
    }, 5000) // 每5秒刷新一次

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
      if (socket) {
        socket.close()
      }
      clearInterval(phaseRefreshInterval)
    }
  }, [])

  const value = {
    socket,
    isConnected,
    connectionStatus,
    dashboardData,
    sendMessage,
    forceUpdate,
    executeAgentCommand,
    evaluatePool,
    emergencyExit,
    reconnect: connectWebSocket
  }

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  )
}

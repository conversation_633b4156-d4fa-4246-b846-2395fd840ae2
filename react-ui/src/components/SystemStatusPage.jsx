import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Progress } from './ui/progress'
import { 
  Activity, AlertTriangle, TrendingUp, Shield, 
  Wallet, Database, Wifi, Bot, DollarSign,
  Target, BarChart3, Clock
} from 'lucide-react'

const SystemStatusPage = () => {
  const [agentStatus, setAgentStatus] = useState({})
  const [lpPositions, setLpPositions] = useState([])
  const [riskMetrics, setRiskMetrics] = useState({})
  const [systemHealth, setSystemHealth] = useState({})
  const [realtimeData, setRealtimeData] = useState({})

  useEffect(() => {
    // 模擬實時 Agent 狀態
    const mockAgentStatus = {
      MarketIntelAgent: {
        status: 'active',
        currentTask: '正在掃描 Meteora 新池子...',
        lastUpdate: '2 秒前',
        progress: 75
      },
      PortfolioManagerAgent: {
        status: 'active', 
        currentTask: '分析 TRUMP/USDC 池子風險評估',
        lastUpdate: '5 秒前',
        progress: 60
      },
      RiskSentinelAgent: {
        status: 'monitoring',
        currentTask: '監控 IL 變化: -2.3% (安全範圍)',
        lastUpdate: '1 秒前',
        progress: 100
      },
      ExecutionAgent: {
        status: 'standby',
        currentTask: '待命中，等待投資指令',
        lastUpdate: '30 秒前',
        progress: 0
      },
      StrategyAgent: {
        status: 'active',
        currentTask: '優化 SOL/USDC 池子配置',
        lastUpdate: '8 秒前',
        progress: 45
      },
      HealthGuardAgent: {
        status: 'monitoring',
        currentTask: '系統健康檢查 - 所有服務正常',
        lastUpdate: '3 秒前',
        progress: 100
      },
      SupervisorAgent: {
        status: 'coordinating',
        currentTask: '協調 Agent 間通訊',
        lastUpdate: '1 秒前',
        progress: 100
      }
    }

    // 模擬當前 LP 持倉
    const mockLpPositions = [
      {
        id: 1,
        pair: 'SOL/USDC',
        chain: 'Solana',
        invested: 50000,
        currentValue: 52300,
        pnl: 2300,
        pnlPercent: 4.6,
        apr: 156.8,
        il: -1.2,
        status: 'active'
      },
      {
        id: 2,
        pair: 'BNB/USDT', 
        chain: 'BSC',
        invested: 30000,
        currentValue: 31200,
        pnl: 1200,
        pnlPercent: 4.0,
        apr: 45.2,
        il: -0.8,
        status: 'active'
      },
      {
        id: 3,
        pair: 'CAKE/BNB',
        chain: 'BSC', 
        invested: 20000,
        currentValue: 19400,
        pnl: -600,
        pnlPercent: -3.0,
        apr: 78.5,
        il: -4.2,
        status: 'warning'
      }
    ]

    // 模擬風險指標
    const mockRiskMetrics = {
      totalIL: -2.1,
      var95: 3.2,
      maxDrawdown: -5.8,
      riskScore: 7.2,
      healthScore: 92
    }

    // 模擬系統健康狀態
    const mockSystemHealth = {
      apis: { meteora: true, pancakeswap: true, coingecko: true },
      wallet: { connected: true, balance: 125000 },
      database: { connected: true, latency: 45 },
      network: { bsc: true, solana: true }
    }

    // 模擬實時收益數據
    const mockRealtimeData = {
      todayPnl: 2900,
      totalPnl: 15600,
      todayFees: 450,
      totalFees: 3200,
      totalInvested: 100000,
      totalValue: 102900
    }

    setAgentStatus(mockAgentStatus)
    setLpPositions(mockLpPositions)
    setRiskMetrics(mockRiskMetrics)
    setSystemHealth(mockSystemHealth)
    setRealtimeData(mockRealtimeData)

    // 每 3 秒更新一次數據
    const interval = setInterval(() => {
      // 更新 Agent 狀態
      setAgentStatus(prev => {
        const updated = { ...prev }
        Object.keys(updated).forEach(agent => {
          if (updated[agent].status === 'active') {
            updated[agent].progress = Math.min(100, updated[agent].progress + Math.random() * 10)
            updated[agent].lastUpdate = `${Math.floor(Math.random() * 10)} 秒前`
          }
        })
        return updated
      })
    }, 3000)

    return () => clearInterval(interval)
  }, [])

  const getAgentStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-blue-100 text-blue-800">執行中</Badge>
      case 'monitoring':
        return <Badge className="bg-green-100 text-green-800">監控中</Badge>
      case 'standby':
        return <Badge className="bg-gray-100 text-gray-800">待命</Badge>
      case 'coordinating':
        return <Badge className="bg-purple-100 text-purple-800">協調中</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  const getPositionStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">正常</Badge>
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800">警告</Badge>
      case 'danger':
        return <Badge className="bg-red-100 text-red-800">風險</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 左側 - 實時 Agent 工作狀態 */}
      <div className="space-y-6">
        {/* Agent 實時狀態 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bot className="h-5 w-5 text-blue-500" />
              <span>AI Agent 實時狀態</span>
            </CardTitle>
            <CardDescription>
              各 Agent 當前正在執行的任務
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(agentStatus).map(([agentName, data]) => (
              <div key={agentName} className="border rounded-lg p-3">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-sm">{agentName}</span>
                    {getAgentStatusBadge(data.status)}
                  </div>
                  <span className="text-xs text-gray-500">{data.lastUpdate}</span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{data.currentTask}</p>
                {data.status === 'active' && (
                  <div className="flex items-center space-x-2">
                    <Progress value={data.progress} className="flex-1 h-2" />
                    <span className="text-xs text-gray-500">{data.progress}%</span>
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>

        {/* 系統健康監控 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-green-500" />
              <span>系統健康監控</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* API 連接狀態 */}
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center">
                <Wifi className="h-4 w-4 mr-1" />
                API 連接狀態
              </h4>
              <div className="grid grid-cols-3 gap-2">
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${systemHealth.apis?.meteora ? 'bg-green-500' : 'bg-red-500'}`} />
                  <span className="text-xs">Meteora</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${systemHealth.apis?.pancakeswap ? 'bg-green-500' : 'bg-red-500'}`} />
                  <span className="text-xs">PancakeSwap</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${systemHealth.apis?.coingecko ? 'bg-green-500' : 'bg-red-500'}`} />
                  <span className="text-xs">CoinGecko</span>
                </div>
              </div>
            </div>

            {/* 錢包狀態 */}
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center">
                <Wallet className="h-4 w-4 mr-1" />
                錢包狀態
              </h4>
              <div className="flex justify-between text-sm">
                <span>連接狀態:</span>
                <span className={systemHealth.wallet?.connected ? 'text-green-600' : 'text-red-600'}>
                  {systemHealth.wallet?.connected ? '已連接' : '未連接'}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>餘額:</span>
                <span>${systemHealth.wallet?.balance?.toLocaleString()}</span>
              </div>
            </div>

            {/* 數據庫狀態 */}
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center">
                <Database className="h-4 w-4 mr-1" />
                數據庫狀態
              </h4>
              <div className="flex justify-between text-sm">
                <span>連接:</span>
                <span className={systemHealth.database?.connected ? 'text-green-600' : 'text-red-600'}>
                  {systemHealth.database?.connected ? '正常' : '異常'}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>延遲:</span>
                <span>{systemHealth.database?.latency}ms</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 右側 - 關鍵業務信息 */}
      <div className="space-y-6">
        {/* 實時收益概覽 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              <span>實時收益概覽</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  +${realtimeData.todayPnl?.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">今日收益</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  +${realtimeData.totalPnl?.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">總收益</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold">
                  ${realtimeData.todayFees?.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">今日手續費</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold">
                  ${realtimeData.totalFees?.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">總手續費</div>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-between text-sm">
                <span>總投入:</span>
                <span>${realtimeData.totalInvested?.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm font-medium">
                <span>當前價值:</span>
                <span>${realtimeData.totalValue?.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>總收益率:</span>
                <span className="text-green-600">
                  +{((realtimeData.totalValue - realtimeData.totalInvested) / realtimeData.totalInvested * 100).toFixed(2)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 當前 LP 持倉 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-purple-500" />
              <span>當前 LP 持倉</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {lpPositions.map(position => (
                <div key={position.id} className="border rounded-lg p-3">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{position.pair}</span>
                        <Badge variant="outline" className="text-xs">{position.chain}</Badge>
                        {getPositionStatusBadge(position.status)}
                      </div>
                      <div className="text-sm text-gray-500">
                        APR: {position.apr}% | IL: {position.il}%
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`font-medium ${position.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {position.pnl >= 0 ? '+' : ''}${position.pnl.toLocaleString()}
                      </div>
                      <div className={`text-sm ${position.pnlPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {position.pnlPercent >= 0 ? '+' : ''}{position.pnlPercent}%
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>投入: ${position.invested.toLocaleString()}</span>
                    <span>當前: ${position.currentValue.toLocaleString()}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 風險監控 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-orange-500" />
              <span>風險監控</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-500">總 IL</div>
                <div className={`text-lg font-semibold ${riskMetrics.totalIL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {riskMetrics.totalIL}%
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500">VaR 95%</div>
                <div className="text-lg font-semibold">{riskMetrics.var95}%</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">最大回撤</div>
                <div className="text-lg font-semibold text-red-600">{riskMetrics.maxDrawdown}%</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">風險評分</div>
                <div className="text-lg font-semibold">{riskMetrics.riskScore}/10</div>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-1">
                <span>系統健康度</span>
                <span>{riskMetrics.healthScore}%</span>
              </div>
              <Progress value={riskMetrics.healthScore} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SystemStatusPage

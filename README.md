# 🚀 DyFlow v3.1 - 24/7 自动化单边LP策略系统

基于Agno Framework的低流通Meme币LP策略系统，支持BSC (PancakeSwap v3) 和 Solana (Meteora DLMM v2)。

## ✨ 主要特性

- 🤖 **智能Agent系统**: 基于Agno Framework的多Agent协作
- 🔄 **完整交易循环**: SOL ↔ DLMM LP头寸自动管理
- 🎯 **四种LP策略**: 对称流动性、曲线分布、买卖价差、单边流动性
- 🌐 **统一Web界面**: React + FastAPI实时监控Dashboard
- 📊 **真实数据集成**: PancakeSwap v3 + Meteora DLMM v2 API
- 🛡️ **风险管理**: IL熔断、VaR监控、自动收割

## 🚀 快速开始

### 统一启动方式 (推荐)
```bash
# 安装依赖
pip install -r requirements.txt

# 运行核心系统
python dyflow.py core

# 运行Web UI
python dyflow.py ui

# 运行测试
python dyflow.py test

# 查看系统状态
python dyflow.py status
```

### 传统启动方式 (仍然支持)
```bash
# 启动后端服务
python dyflow_real_data_backend.py

# 启动React UI
cd react-ui && npm run build && cd ..
python start_dyflow_ui.py
```

## 📋 命令参考

### 核心命令
```bash
python dyflow.py core                    # 运行核心系统
python dyflow.py ui                      # 运行Web UI (自动选择)
python dyflow.py ui --mode react         # 运行React UI
python dyflow.py ui --mode simple        # 运行简单UI
python dyflow.py backend                 # 仅运行后端服务
python dyflow.py test                    # 运行基本测试
python dyflow.py test --type complete    # 运行完整测试
python dyflow.py status                  # 显示系统状态
python dyflow.py help                    # 显示详细帮助
```

### 配置选项
```bash
--config PATH        # 指定配置文件路径
--log-level LEVEL    # 设置日志级别 (DEBUG, INFO, WARNING, ERROR)
```

### 访问地址
- **Web Dashboard**: http://localhost:8000
- **React UI**: http://localhost:3000 (开发模式)
- **API文档**: http://localhost:8000/docs

## 🎯 核心功能

### 🤖 智能Agent系统
- **TradingExecutorAgent**: 主要交易执行Agent，支持完整的swap和LP策略部署
- **PlannerAgent**: 策略规划和决策Agent
- **RiskSentinelAgent**: 风险监控和预警Agent
- **ScorerAgent**: 池子评分和筛选Agent

### 🔄 交易功能
- **Jupiter Swap**: 支持Solana上所有SPL代币交换
- **批量交换**: 一次执行多个交换操作
- **DCA交换**: 分批执行降低价格影响
- **最优路由**: 自动选择最佳交换路径

### 🎯 LP策略部署
1. **SPOT_BALANCED** (对称流动性) - 低风险，15% APR
2. **CURVE_BALANCED** (曲线分布) - 中等风险，25% APR
3. **BID_ASK_BALANCED** (买卖价差) - 中等风险，30% APR
4. **SPOT_IMBALANCED** (单边流动性) - 高风险，40% APR

### 🔄 完整循环管理
- **SOL → DLMM LP**: 从SOL开始，自动交换并部署LP策略
- **DLMM LP → SOL**: 收割费用、退出持仓、转换回SOL
- **自动收割**: 定期收割手续费收入
- **一键平仓**: 紧急情况下快速退出

### 📊 实时监控
- **池子扫描**: 实时扫描BSC和Solana上的LP池子
- **数据更新**: 3-5秒自动更新频率
- **WebSocket**: 实时数据推送
- **系统健康**: 完整的系统状态监控

## 📊 数据源

### BSC数据
- **主要来源**: PancakeSwap v3 Subgraph API
- **API端点**: https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ
- **更新频率**: 15秒
- **支持协议**: PancakeSwap v3

### Solana数据
- **主要来源**: Meteora DLMM v2 API
- **API端点**: https://dammv2-api.meteora.ag
- **更新频率**: 12秒
- **支持协议**: Meteora DLMM v2, Orca, Raydium

## 🏗️ 技术架构

### 后端技术栈
- **框架**: FastAPI + AsyncIO
- **AI框架**: Agno Framework + Ollama
- **数据库**: Supabase (PostgreSQL)
- **区块链**: Solana Web3.py, BSC Web3.py
- **API集成**: Jupiter SDK, Meteora SDK

### 前端技术栈
- **React**: TypeScript + Vite
- **样式**: Tailwind CSS
- **状态管理**: React Hooks
- **实时通信**: WebSocket
- **图表**: Chart.js / Recharts

### 部署架构
- **容器化**: Docker + Docker Compose
- **监控**: Prometheus + Grafana (可选)
- **日志**: Structured Logging (structlog)
- **配置**: YAML + 环境变量

## 📁 项目结构

详细的项目结构请参考 [PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md)

```
dyflow_new/
├── dyflow.py                      # 🆕 统一启动器
├── src/                          # 核心代码
│   ├── agents/                   # 智能代理
│   ├── tools/                    # 工具集
│   ├── utils/                    # 工具函数
│   └── supervisor.py             # 主调度器
├── config/                       # 配置文件
│   └── unified_config.py         # 🆕 统一配置管理
├── web_ui/                       # Web界面
│   └── unified_app.py            # 🆕 统一Web应用
├── react-ui/                     # React界面
├── tests/                        # 测试文件
├── examples/                     # 示例代码
└── docs/                         # 文档
```

## ⚙️ 环境配置

### 必需环境变量
```bash
export SUPABASE_URL="your_supabase_url"
export SUPABASE_ANON_KEY="your_supabase_anon_key"
export SUPABASE_SERVICE_ROLE_KEY="your_service_role_key"

# 可选配置
export BSC_RPC_URL="https://bsc-dataseed.binance.org/"
export SOLANA_RPC_URL="https://api.mainnet-beta.solana.com"
export LOG_LEVEL="INFO"
```

### 依赖安装
```bash
# Python依赖
pip install -r requirements.txt

# Ollama (本地AI模型)
curl -fsSL https://ollama.ai/install.sh | sh
ollama pull qwen2.5:3b

# React UI依赖 (可选)
cd react-ui && npm install
```

## 🧪 测试

### 运行测试
```bash
# 基本功能测试
python dyflow.py test --type basic

# 完整系统测试
python dyflow.py test --type complete

# 交易执行器测试
python dyflow.py test --type trading

# 真实API集成测试
python dyflow.py test --type real_api
```

## 🚀 部署

### 开发环境
```bash
python dyflow.py core --log-level DEBUG
```

### 生产环境
```bash
# 使用生产配置
python dyflow.py core --config config/production.yaml

# 后台运行
nohup python dyflow.py core > logs/dyflow.log 2>&1 &
```

### Docker部署
```bash
# 构建镜像
docker build -t dyflow:latest .

# 运行容器
docker-compose up -d
```

## 📚 文档

- [项目结构文档](PROJECT_STRUCTURE.md)
- [交易执行器文档](docs/TradingExecutorAgent_README.md)
- [API集成总结](REAL_API_INTEGRATION_SUMMARY.md)
- [部署指南](docs/DEPLOYMENT.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证。

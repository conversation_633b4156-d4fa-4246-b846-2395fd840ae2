import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Progress } from './ui/progress'
import { 
  CheckCircle, 
  XCircle, 
  Loader, 
  Clock,
  Settings,
  Shield,
  Globe,
  Wallet,
  BarChart3,
  Target,
  Zap,
  AlertTriangle
} from 'lucide-react'

const PhaseMonitor = ({ phaseStatus, currentPhase }) => {
  // 調試信息
  console.log('🔍 PhaseMonitor 接收到的數據:', { phaseStatus, currentPhase })

  // 如果沒有數據，使用模擬數據進行測試
  const mockPhaseStatus = {
    0: { status: 'completed', started_at: '2025-06-16T04:00:00Z', completed_at: '2025-06-16T04:00:05Z', error: null },
    1: { status: 'completed', started_at: '2025-06-16T04:00:05Z', completed_at: '2025-06-16T04:00:15Z', error: null },
    2: { status: 'completed', started_at: '2025-06-16T04:00:15Z', completed_at: '2025-06-16T04:00:20Z', error: null },
    3: { status: 'completed', started_at: '2025-06-16T04:00:20Z', completed_at: '2025-06-16T04:00:35Z', error: null },
    4: { status: 'completed', started_at: '2025-06-16T04:00:35Z', completed_at: '2025-06-16T04:00:45Z', error: null },
    5: { status: 'completed', started_at: '2025-06-16T04:00:45Z', completed_at: '2025-06-16T04:00:50Z', error: null },
    6: { status: 'running', started_at: '2025-06-16T04:00:50Z', completed_at: null, error: null },
    7: { status: 'pending', started_at: null, completed_at: null, error: null },
    8: { status: 'pending', started_at: null, completed_at: null, error: null }
  }

  // 使用傳入的數據，如果沒有則使用模擬數據
  const actualPhaseStatus = phaseStatus && Object.keys(phaseStatus).length > 0 ? phaseStatus : mockPhaseStatus
  const actualCurrentPhase = currentPhase !== undefined ? currentPhase : 6
  const phases = [
    {
      id: 0,
      name: 'Phase 0: 系統初始化',
      description: 'SupervisorAgent - 讀取 YAML/ENV、Vault 發放密鑰',
      icon: Settings,
      color: 'blue'
    },
    {
      id: 1,
      name: 'Phase 1: 健康檢查',
      description: 'HealthGuardAgent + ConnProbeTool - RPC/Subgraph/DB/UI ≥ 90% 健康',
      icon: Shield,
      color: 'green'
    },
    {
      id: 2,
      name: 'Phase 2: UI 啟動',
      description: 'WebUI & PrometheusExporter - http://localhost:3000 200 OK',
      icon: Globe,
      color: 'purple'
    },
    {
      id: 3,
      name: 'Phase 3: 錢包測試',
      description: 'WalletProbeTool - MPC 簽名 & nonce 測試通過',
      icon: Wallet,
      color: 'yellow'
    },
    {
      id: 4,
      name: 'Phase 4: 市場情報',
      description: 'MarketIntelAgent - NATS bus.pool 開始推送池事件',
      icon: BarChart3,
      color: 'indigo'
    },
    {
      id: 5,
      name: 'Phase 5: 投資組合',
      description: 'PortfolioManagerAgent - NAV ≥ 0 且資金鎖可寫入',
      icon: Target,
      color: 'pink'
    },
    {
      id: 6,
      name: 'Phase 6: 策略生成',
      description: 'StrategyAgent - 產生 LPPlan.approved',
      icon: Zap,
      color: 'orange'
    },
    {
      id: 7,
      name: 'Phase 7: 交易執行',
      description: 'ExecutionAgent - Tx ≥ 1 筆成功廣播',
      icon: CheckCircle,
      color: 'teal'
    },
    {
      id: 8,
      name: 'Phase 8: 風控監控',
      description: 'RiskSentinelAgent + FeeCollectorTool - IL_net、VaR 均在限內',
      icon: AlertTriangle,
      color: 'red'
    }
  ]

  const getPhaseStatus = (phaseId) => {
    // 使用實際數據
    if (!actualPhaseStatus || typeof actualPhaseStatus !== 'object' || !actualPhaseStatus[phaseId.toString()]) {
      return { status: 'pending', started_at: null, completed_at: null, error: null }
    }
    return actualPhaseStatus[phaseId.toString()]
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'running':
        return <Loader className="h-5 w-5 text-blue-500 animate-spin" />
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">已完成</Badge>
      case 'running':
        return <Badge className="bg-blue-100 text-blue-800">運行中</Badge>
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">失敗</Badge>
      default:
        return <Badge variant="outline">等待中</Badge>
    }
  }

  const getColorClasses = (color, status) => {
    const baseColors = {
      blue: 'border-blue-200 bg-blue-50',
      green: 'border-green-200 bg-green-50',
      purple: 'border-purple-200 bg-purple-50',
      yellow: 'border-yellow-200 bg-yellow-50',
      indigo: 'border-indigo-200 bg-indigo-50',
      pink: 'border-pink-200 bg-pink-50',
      orange: 'border-orange-200 bg-orange-50',
      teal: 'border-teal-200 bg-teal-50',
      red: 'border-red-200 bg-red-50'
    }

    if (status === 'completed') {
      return 'border-green-300 bg-green-100'
    } else if (status === 'running') {
      return 'border-blue-300 bg-blue-100'
    } else if (status === 'failed') {
      return 'border-red-300 bg-red-100'
    }

    return baseColors[color] || 'border-gray-200 bg-gray-50'
  }

  const completedPhases = phases.filter(phase => {
    const status = getPhaseStatus(phase.id)
    return status.status === 'completed'
  }).length

  const progressPercentage = (completedPhases / phases.length) * 100

  return (
    <div className="space-y-6">
      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-blue-500" />
            <span>DyFlow v3.3 八階段啟動序列</span>
          </CardTitle>
          <CardDescription>
            系統啟動進度 - 當前階段: Phase {actualCurrentPhase}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">整體進度</span>
              <span className="text-sm text-gray-500">
                {completedPhases}/{phases.length} 階段完成
              </span>
            </div>
            <Progress value={progressPercentage} className="w-full" />
            <div className="text-lg font-semibold">
              {progressPercentage.toFixed(1)}% 完成
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Phase Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {phases.map((phase, index) => {
          const status = getPhaseStatus(phase.id)
          const IconComponent = phase.icon
          
          return (
            <motion.div
              key={phase.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={`${getColorClasses(phase.color, status.status)} transition-all duration-300`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <IconComponent className={`h-5 w-5 text-${phase.color}-600`} />
                      <span className="font-medium text-sm">{phase.name}</span>
                    </div>
                    {getStatusIcon(status.status)}
                  </div>
                  {getStatusBadge(status.status)}
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-xs text-gray-600 mb-3">
                    {phase.description}
                  </p>
                  
                  {status.started_at && (
                    <div className="text-xs text-gray-500 space-y-1">
                      <div>開始: {new Date(status.started_at).toLocaleTimeString()}</div>
                      {status.completed_at && (
                        <div>完成: {new Date(status.completed_at).toLocaleTimeString()}</div>
                      )}
                      {status.error && (
                        <div className="text-red-600">錯誤: {status.error}</div>
                      )}
                    </div>
                  )}
                  
                  {phase.id === actualCurrentPhase && status.status === 'running' && (
                    <div className="mt-2">
                      <div className="flex items-center space-x-2">
                        <Loader className="h-3 w-3 animate-spin text-blue-500" />
                        <span className="text-xs text-blue-600">正在執行...</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </div>

      {/* Phase Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>啟動時間軸</CardTitle>
          <CardDescription>各階段執行時間記錄</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {phases.map((phase, index) => {
              const status = getPhaseStatus(phase.id)
              const IconComponent = phase.icon
              
              return (
                <div key={phase.id} className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {getStatusIcon(status.status)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{phase.name}</span>
                      {status.started_at && (
                        <span className="text-xs text-gray-500">
                          {new Date(status.started_at).toLocaleTimeString()}
                        </span>
                      )}
                    </div>
                    {status.error && (
                      <div className="text-xs text-red-600 mt-1">
                        {status.error}
                      </div>
                    )}
                  </div>
                  <div className="flex-shrink-0">
                    {getStatusBadge(status.status)}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default PhaseMonitor

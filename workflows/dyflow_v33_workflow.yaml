# DyFlow v3.3 Workflow Configuration
# 實現 PRD v3.3 定義的 NATS 消息總線和 Agent 協調

# NATS 消息通道配置
channels:
  - name: bus.pool
    schema: pool.json
    persist: yes
    dead_letter: bus.dead
    description: "池子數據和排名更新事件"
    
  - name: bus.plan.raw
    schema: plan.json
    persist: no
    description: "原始策略計劃"
    
  - name: bus.plan.approved
    schema: plan.json
    persist: yes
    description: "已批准的策略計劃"
    
  - name: bus.tx
    schema: tx.json
    persist: yes
    description: "交易執行事件"
    
  - name: bus.risk
    schema: risk.json
    persist: yes
    description: "風險警報和監控事件"
    
  - name: bus.health
    schema: health.json
    persist: no
    description: "系統健康狀態"
    
  - name: bus.dead
    schema: dead_letter.json
    persist: yes
    description: "死信隊列"

# 全局配置
globals:
  trading_mode: active  # active | exit_only | paused
  portfolio:
    lock_scope: pool
    max_positions: 10
    position_size_usd: 5000
  guard_policies:
    default_timeout: 4
    default_retries: 2
    max_execution_time: 300
  risk_limits:
    il_net_threshold: -8.0  # -8%
    var_95_threshold: 4.0   # 4%
    volatility_threshold: 5.0  # 5%
    health_score_threshold: 0.8  # 80%

# Agent 配置
agents:
  # SupervisorAgent - Phase 0
  - id: supervisor
    class: SupervisorAgent
    priority: 10
    config:
      startup_phases: 8
      rollback_on_failure: true
    
  # HealthGuardAgent - Phase 1
  - id: health
    class: HealthGuardAgent
    priority: 9
    publish:
      channel: bus.health
      interval: 60
    config:
      check_interval: 60
      components:
        - bsc_rpc
        - solana_rpc
        - pancake_subgraph
        - meteora_api
        - database
    
  # MarketIntelAgent - Phase 4
  - id: intel
    class: MarketIntelAgent
    priority: 6
    tools:
      - damm_scanner
      - chain_scanner
    subscribe:
      - bus.pool
    publish:
      channel: bus.plan.raw
      priority: 6
    config:
      scan_interval: 30
      max_pools: 100
      filters:
        min_tvl: 10000000
        max_created_days: 2
        min_fee_tvl_ratio: 0.05
        min_fees_24h: 5
    
  # PortfolioManagerAgent - Phase 5
  - id: portfolio
    class: PortfolioManagerAgent
    priority: 7
    subscribe:
      - bus.plan.approved
      - bus.tx
    config:
      nav_update_interval: 300
      rebalance_threshold: 0.02
      max_allocation_per_pool: 0.15
    
  # StrategyAgent - Phase 6
  - id: strategy
    class: StrategyAgent
    priority: 8
    tools:
      - vol_oracle
      - tx_sim
    subscribe:
      - bus.plan.raw
    guards:
      expects:
        sigma_p90:
          lt: 0.60
        il_sim:
          lt: 0.05
      timeout: 120
      retries: 2
    publish:
      channel: bus.plan.approved
      priority: 8
    config:
      strategy_types:
        - SPOT_BALANCED
        - CURVE_BALANCED
        - BID_ASK_BALANCED
        - SPOT_IMBALANCED_DAMM
      risk_assessment: true
    
  # ExecutionAgent - Phase 7
  - id: exec
    class: ExecutionAgent
    priority: 9
    tools:
      - wallet_signer
      - dex_router
    subscribe:
      - bus.plan.approved
    publish:
      channel: bus.tx
    config:
      max_concurrent_tx: 3
      tx_timeout: 180
      gas_price_strategy: "medium"
      slippage_tolerance: 0.005
    
  # RiskSentinelAgent - Phase 8
  - id: risk
    class: RiskSentinelAgent
    priority: 10
    subscribe:
      - bus.tx
      - bus.pool
    publish:
      channel: bus.risk
    config:
      monitoring_interval: 60
      il_check_interval: 300
      var_calculation_method: "historical"
      emergency_exit_enabled: true

# Tool 配置
tools:
  # Meteora DLMM/DAMM v2 池掃描
  - id: damm_scanner
    class: DammScannerTool
    config:
      meteora_api_url: "https://dlmm-api.meteora.ag"
      timeout: 30
      max_pools: 100
      filters:
        min_tvl: 10000000
        max_created_days: 2
        min_fee_tvl_ratio: 0.05
        min_fees_24h: 5
  
  # BSC 和 Solana 鏈掃描
  - id: chain_scanner
    class: ChainScannerTool
    config:
      bsc_rpc_url: "https://bsc-dataseed.binance.org/"
      solana_rpc_url: "https://api.mainnet-beta.solana.com"
      pancake_subgraph_url: "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
      timeout: 30
  
  # 波動率預言機
  - id: vol_oracle
    class: VolOracleTool
    config:
      update_interval: 60
      lookback_periods:
        - 1h
        - 4h
        - 24h
      volatility_models:
        - parkinson
        - atr
        - realized
  
  # 錢包簽名工具
  - id: wallet_signer
    class: WalletSignerTool
    config:
      mpc_threshold: "2/3"
      signature_timeout: 30
      backup_wallets: true
      transaction_limits:
        daily_limit_usd: 100000
        single_tx_limit_usd: 10000
  
  # DEX 路由工具
  - id: dex_router
    class: DexRouterTool
    config:
      jupiter_api_url: "https://quote-api.jup.ag/v6"
      pancake_router_address: "******************************************"
      slippage_tolerance: 0.005
      max_hops: 3
  
  # 交易模擬工具
  - id: tx_sim
    class: TxSimTool
    config:
      tenderly_api_key: "${TENDERLY_API_KEY}"
      solsim_api_key: "${SOLSIM_API_KEY}"
      simulation_timeout: 15
  
  # 連接探測工具
  - id: conn_probe
    class: ConnProbeTool
    config:
      probe_interval: 30
      timeout: 10
      endpoints:
        - bsc_rpc
        - solana_rpc
        - pancake_subgraph
        - meteora_api
        - database
  
  # 手續費收割工具
  - id: fee_collector
    class: FeeCollectorTool
    schedule: "0 2 * * *"  # 每日 UTC 02:00
    subscribe:
      - bus.tx
    config:
      collection_timeout: 300
      min_fee_threshold: 1.0
      auto_compound: true

# 策略映射矩陣 (v3.3)
strategy_mapping:
  SPOT_BALANCED:
    conditions:
      volatility_max: 0.01  # σ ≤ 1%
      fee_tvl_ratio_max: 0.002  # Fee/TVL < 0.2%
    k_calculation: "1 * ATR"
    description: "雙穩、主流代幣對"
    risk_level: "low"
    
  CURVE_BALANCED:
    conditions:
      volatility_min: 0.01  # 1-3%
      volatility_max: 0.03
      fee_activation: false
    k_calculation: "1.5 * ATR"
    description: "隨趨勢滑動"
    risk_level: "medium"
    
  BID_ASK_BALANCED:
    conditions:
      spread_max: 0.0005  # Spread < 5 bps
      depth_volume_ratio: "high"  # Depth ≫ Vol
    k_calculation: "2 * ATR"
    description: "吃點差策略"
    risk_level: "medium"
    
  SPOT_IMBALANCED_DAMM:
    conditions:
      volatility_min: 0.03  # σ > 3%
      fee_tvl_ratio_min: 0.004  # Fee/TVL ≥ 0.4%
      fdv_max: 1000000  # FDV < 1M
    k_calculation: "max(2 * ATR, 1.2 * σ_p90)"
    description: "單邊流動性，費率可抵 IL"
    risk_level: "high"

# 風控配置
risk_management:
  il_net_formula: "IL_raw - FeeAccrued"
  thresholds:
    il_net_exit: -8.0  # IL_net < -8% 強平
    var_95_warning: 4.0  # VaR_95 > 4% 降倉 25%
    volatility_exit: 5.0  # σ_1m > 5% 持續 3 分鐘切至 exit_only
    health_score_pause: 0.8  # Health score < 0.8 全局 paused
  
  monitoring:
    il_check_interval: 300  # 5 分鐘檢查一次
    var_calculation_window: 24h
    volatility_window: 1m
    health_check_interval: 60

# 數據模型 Schema
schemas:
  pool.json:
    type: object
    properties:
      id: {type: string}
      chain: {type: string, enum: [bsc, solana]}
      protocol: {type: string}
      tvl_usd: {type: number}
      volume_24h_usd: {type: number}
      fees_24h_usd: {type: number}
      apr: {type: number}
      score: {type: number}
      strategy_type: {type: string}
      rank: {type: integer}
    required: [id, chain, protocol, tvl_usd]
  
  plan.json:
    type: object
    properties:
      id: {type: string}
      pool_id: {type: string}
      strategy_type: {type: string}
      allocation_amount: {type: number}
      priority: {type: integer}
      expected_apy: {type: number}
      risk_level: {type: string}
      status: {type: string, enum: [pending, approved, rejected]}
    required: [id, pool_id, strategy_type, allocation_amount]
  
  tx.json:
    type: object
    properties:
      id: {type: string}
      plan_id: {type: string}
      tx_hash: {type: string}
      chain: {type: string}
      status: {type: string, enum: [pending, confirmed, failed]}
      gas_used: {type: number}
      timestamp: {type: string, format: date-time}
    required: [id, plan_id, chain, status]

# 部署配置
deployment:
  docker_compose: true
  services:
    - nats:4222
    - supervisor
    - health_guard
    - market_intel
    - portfolio_manager
    - strategy_agent
    - execution_agent
    - risk_sentinel
  
  monitoring:
    prometheus_port: 9090
    grafana_port: 3001
    metrics_path: "/:chain/:pool_id/metrics"
  
  secrets:
    vault_enabled: true
    dynamic_lease: true
    auto_revoke: true

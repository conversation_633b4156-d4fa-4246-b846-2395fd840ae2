#!/usr/bin/env python3
"""
測試 Agno Framework Agent 通訊
驗證可以替代 NATS 消息總線
"""

import asyncio
from datetime import datetime

def test_ollama_connection():
    """測試 Ollama 連接"""
    print("🔗 測試 Ollama 連接...")
    
    try:
        import requests
        response = requests.get('http://localhost:11434/api/version', timeout=5)
        if response.status_code == 200:
            print("✅ Ollama 服務器運行中")
            return True
        else:
            print(f"❌ Ollama 服務器響應異常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama 連接失敗: {e}")
        return False

def test_single_agent():
    """測試單個 Agent"""
    print("\n🤖 測試單個 Agent...")
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        
        # 創建 Agent
        agent = Agent(
            name="DyFlowTestAgent",
            model=Ollama(id="qwen3:latest", host="http://localhost:11434"),
            instructions=[
                "You are a DyFlow system test agent.",
                "Respond concisely to confirm system functionality.",
                "Always include 'DyFlow-Test-OK' in your response."
            ]
        )
        
        print("✅ Agent 創建成功")
        
        # 測試 Agent 執行
        print("🔄 測試 Agent 執行...")
        response = agent.run("Please confirm you are working by responding with system status.")
        
        if response and response.content:
            print("✅ Agent 執行成功")
            print(f"📝 響應: {response.content[:150]}...")
            
            # 檢查是否包含測試標記
            if "DyFlow-Test-OK" in response.content:
                print("✅ Agent 遵循指令成功")
            else:
                print("⚠️  Agent 未完全遵循指令")
            
            return True
        else:
            print("❌ Agent 執行失敗 - 無響應")
            return False
            
    except Exception as e:
        print(f"❌ 單個 Agent 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_agents():
    """測試多個 Agent 協作"""
    print("\n👥 測試多個 Agent 協作...")
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        
        # 創建市場分析 Agent
        market_agent = Agent(
            name="MarketAgent",
            model=Ollama(id="qwen3:latest", host="http://localhost:11434"),
            instructions=[
                "You are a DeFi market analyst.",
                "Provide brief market analysis.",
                "Always end with 'Market-Analysis-Complete'."
            ]
        )
        
        # 創建策略 Agent
        strategy_agent = Agent(
            name="StrategyAgent", 
            model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
            instructions=[
                "You are a DeFi strategy planner.",
                "Create strategies based on market input.",
                "Always end with 'Strategy-Plan-Ready'."
            ]
        )
        
        print("✅ 多個 Agent 創建成功")
        
        # 測試順序執行（模擬 workflow）
        print("🔄 測試順序執行...")
        
        # Step 1: 市場分析
        market_response = market_agent.run("Analyze current DeFi market conditions briefly.")
        if not market_response or not market_response.content:
            print("❌ 市場分析失敗")
            return False
        
        print("✅ 市場分析完成")
        print(f"📊 市場分析: {market_response.content[:100]}...")
        
        # Step 2: 策略規劃（使用市場分析結果）
        strategy_prompt = f"""
        Based on this market analysis, create a brief LP strategy:
        
        Market Analysis: {market_response.content}
        
        Please provide a concise strategy recommendation.
        """
        
        strategy_response = strategy_agent.run(strategy_prompt)
        if not strategy_response or not strategy_response.content:
            print("❌ 策略規劃失敗")
            return False
        
        print("✅ 策略規劃完成")
        print(f"📋 策略計劃: {strategy_response.content[:100]}...")
        
        # 驗證 Agent 間信息傳遞
        if "Market-Analysis-Complete" in market_response.content and "Strategy-Plan-Ready" in strategy_response.content:
            print("✅ Agent 間信息傳遞成功")
        else:
            print("⚠️  Agent 間信息傳遞部分成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 多 Agent 協作測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_simulation():
    """測試 Workflow 模擬"""
    print("\n🔄 測試 Workflow 模擬...")
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        
        # 創建 DyFlow 階段 Agent
        phases = [
            ("InitAgent", "System initialization", "Init-Complete"),
            ("HealthAgent", "Health check", "Health-OK"),
            ("StrategyAgent", "Strategy generation", "Strategy-Generated")
        ]
        
        agents = {}
        for name, role, marker in phases:
            agents[name] = Agent(
                name=name,
                model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
                instructions=[
                    f"You are responsible for {role} in DyFlow system.",
                    f"Provide brief status and end with '{marker}'."
                ]
            )
        
        print(f"✅ 創建了 {len(agents)} 個階段 Agent")
        
        # 模擬 8-phase 啟動序列的前 3 個階段
        workflow_state = {"current_phase": 0, "results": []}
        
        for i, (agent_name, role, marker) in enumerate(phases):
            print(f"🔄 執行 Phase {i}: {role}")
            
            agent = agents[agent_name]
            prompt = f"Execute {role} for DyFlow system. Report status briefly."
            
            response = agent.run(prompt)
            if response and response.content:
                workflow_state["results"].append({
                    "phase": i,
                    "agent": agent_name,
                    "status": "completed",
                    "response": response.content
                })
                workflow_state["current_phase"] = i + 1
                print(f"✅ Phase {i} 完成")
            else:
                print(f"❌ Phase {i} 失敗")
                return False
        
        print("✅ Workflow 模擬成功")
        print(f"📊 完成階段: {workflow_state['current_phase']}/{len(phases)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow 模擬失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 DyFlow Agno Framework 通訊測試")
    print("驗證 Agno 可以替代 NATS 消息總線")
    print("=" * 60)
    print(f"時間: {datetime.now()}")
    print()
    
    # 前置檢查
    if not test_ollama_connection():
        print("\n❌ Ollama 服務器不可用，無法進行測試")
        return False
    
    # 執行測試序列
    tests = [
        ("單個 Agent", test_single_agent),
        ("多 Agent 協作", test_multiple_agents),
        ("Workflow 模擬", test_workflow_simulation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 總體結果: {passed}/{len(results)} 測試通過")
    
    if passed == len(results):
        print("\n🎉 所有測試通過！")
        print("✅ Agno Framework 完全可以替代 NATS 消息總線")
        print("✅ Agent 間通訊功能正常")
        print("✅ 可以實現 DyFlow 8-phase 啟動序列")
        
        print("\n💡 建議架構:")
        print("1. 使用 Agno Agents 替代獨立的 Agent 實現")
        print("2. 使用 Agent 順序執行替代 NATS 消息傳遞")
        print("3. 使用 Agno Workflow 管理 8-phase 序列")
        print("4. 移除 NATS 相關依賴和代碼")
        
        return True
    else:
        print(f"\n⚠️  {len(results) - passed} 個測試失敗")
        print("建議檢查 Ollama 模型和網絡連接")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🚀 準備就緒：可以開始重構 DyFlow 為純 Agno 架構")
        else:
            print("\n❌ 測試未完全通過，需要解決問題")
    except KeyboardInterrupt:
        print("\n\n⏹️  測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試異常: {e}")
        import traceback
        traceback.print_exc()
